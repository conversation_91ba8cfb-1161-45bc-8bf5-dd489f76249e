﻿#ifndef HEATING_MAGNETIC_STIRRER_CONTROLLER_H
#define HEATING_MAGNETIC_STIRRER_CONTROLLER_H

#include "RestInterfaceDriver.h"
#include <memory>
#include <string>
#include <mutex>
#include <map>

#include "HeatingMagneticStirrerDriver.h"
#include "PLCDriver.h"

/**
 * @brief 加热磁力搅拌器控制器类
 * 
 * 实现加热磁力搅拌器设备的REST接口控制，包括：
 * - 开启/停止加热搅拌
 * - 温度和转速设置
 * - 状态查询
 */
class HeatingMagneticStirrerController : public AnalysisRobot::RestInterface::IDeviceController {
public:
    /**
     * @brief 构造函数
     * @param driver 搅拌加热驱动实例
     * @param plcDriver PLC驱动实例（用于冲洗控制）
     */
    explicit HeatingMagneticStirrerController(
        std::shared_ptr<AnalysisRobot::HeatingMagneticStirrer::HeatingMagneticStirrerDriver> driver,
        std::shared_ptr<AnalysisRobot::PLC::PLCDriver> plcDriver = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~HeatingMagneticStirrerController();
    
    // 实现IDeviceController接口
    AnalysisRobot::RestInterface::DeviceStatus getStatus() override;
    AnalysisRobot::RestInterface::TaskInfo executeOperation(const nlohmann::json& request) override;
    AnalysisRobot::RestInterface::TaskInfo queryTask(int taskId) override;

private:
    std::shared_ptr<AnalysisRobot::HeatingMagneticStirrer::HeatingMagneticStirrerDriver> m_driver; // 搅拌加热驱动
    std::shared_ptr<AnalysisRobot::PLC::PLCDriver> m_plcDriver;                                     // PLC驱动（冲洗控制）
    std::map<int, AnalysisRobot::RestInterface::TaskInfo> m_tasks;                                  // 任务存储
    std::mutex m_tasksMutex;                                                                        // 任务锁
    int m_nextTaskId;                                                                               // 下一个任务ID
    
    /**
     * @brief 处理启动操作
     * 执行: 1.加盖操作 2.开启加热 3.开启搅拌 (智能升温: 1200转, 60°C, 30分钟)
     * @param data 请求数据 (需要channel参数)
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo handleStartOperation(const nlohmann::json& data);

    /**
     * @brief 处理停止操作
     * 执行: 1.停止搅拌 2.冲洗动作 3.移盖操作
     * @param data 请求数据 (需要channel参数)
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo handleStopOperation(const nlohmann::json& data);
    

    
    /**
     * @brief 生成任务ID
     * @return 新的任务ID
     */
    int generateTaskId();
    
    /**
     * @brief 创建成功任务
     * @param action 操作名称
     * @param data 数据
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo createSuccessTask(const std::string& action, const nlohmann::json& data = nlohmann::json());
    
    /**
     * @brief 创建错误任务
     * @param action 操作名称
     * @param error 错误信息
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo createErrorTask(const std::string& action, const std::string& error);
    
    /**
     * @brief 获取当前时间字符串
     * @return 时间字符串
     */
    std::string getCurrentTimeString() const;
    

};

#endif // HEATING_MAGNETIC_STIRRER_CONTROLLER_H
