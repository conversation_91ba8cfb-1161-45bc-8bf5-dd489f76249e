#ifndef DEVICE_MANAGER_H
#define DEVICE_MANAGER_H

#include <memory>
#include <vector>
#include <map>
#include <string>
#include <mutex>
#include <nlohmann/json.hpp>
#include "ConfigManager.h"


// 前向声明
namespace AnalysisRobot {
namespace RestInterface {
    class IDeviceController;
}
namespace Balance {
    class BalanceDriver;
}
namespace HeatingMagneticStirrer {
    class HeatingMagneticStirrerDriver;
}
namespace Moisture {
    class MoistureAnalyzerDriver;
}
namespace Robot {
    class RobotDriver;
}
namespace PLC {
    class PLCDriver;
}
}

class BalanceController;
class HeatingMagneticStirrerController;
class MoistureAnalyzerController;
class RobotController;
class SampleEntryController;
class SampleExitController;
class RepoController;
class DosingController;
class VolumeController;
class FilterController;
class StirController;
class ICPEntryController;
class ShakerController;
class FrameController;
class PouringDeviceController;

/**
 * @brief 设备信息结构
 */
struct DeviceInfo {
    std::string name;           // 设备名称
    std::string type;           // 设备类型
    bool enabled;               // 是否启用
    nlohmann::json parameters;     // 设备参数

    DeviceInfo() : enabled(true) {}
};

/**
 * @brief 设备管理器类
 * 
 * 负责管理所有硬件设备的初始化、配置和生命周期
 * 包括：
 * - 磁力搅拌加热器 (11套)
 * - 天平 (2个)
 * - 机器人 (2个)
 * - 其他设备 (各1套)
 */
class DeviceManager {
public:
    /**
     * @brief 构造函数
     */
    DeviceManager();
    
    /**
     * @brief 析构函数
     */
    ~DeviceManager();
    
    /**
     * @brief 初始化设备管理器
     * @param devicesConfig 设备配置结构体
     * @return 是否成功
     */
    bool initialize(const DeviceConfig& devicesConfig);
    
    /**
     * @brief 启动所有设备
     * @return 是否成功
     */
    bool startAllDevices();
    
    /**
     * @brief 停止所有设备
     */
    void stopAllDevices();
    
    /**
     * @brief 连接所有设备
     * @return 是否成功
     */
    bool connectAllDevices();
    
    /**
     * @brief 断开所有设备连接
     */
    void disconnectAllDevices();
    
    /**
     * @brief 获取设备控制器
     * @param deviceName 设备名称
     * @return 设备控制器指针，如果不存在返回nullptr
     */
    std::shared_ptr<AnalysisRobot::RestInterface::IDeviceController> getDeviceController(const std::string& deviceName);
    
    /**
     * @brief 获取所有设备控制器
     * @return 设备控制器映射表
     */
    std::map<std::string, std::shared_ptr<AnalysisRobot::RestInterface::IDeviceController>> getAllDeviceControllers();
    
    /**
     * @brief 获取设备状态
     * @return 设备状态JSON
     */
    nlohmann::json getDevicesStatus() const;
    
    /**
     * @brief 检查所有设备是否已连接
     * @return 是否全部连接
     */
    bool areAllDevicesConnected() const;
    
    /**
     * @brief 获取最后的错误信息
     * @return 错误信息
     */
    std::string getLastError() const;

private:
    // 硬件驱动实例
    std::vector<std::shared_ptr<AnalysisRobot::Balance::BalanceDriver>> m_balanceDrivers;                    // 天平驱动 (2个)
    std::vector<std::shared_ptr<AnalysisRobot::HeatingMagneticStirrer::HeatingMagneticStirrerDriver>> m_stirrerDrivers; // 搅拌加热驱动 (11个)
    std::shared_ptr<AnalysisRobot::Moisture::MoistureAnalyzerDriver> m_moistureDriver;              // 水分测定仪驱动
    std::vector<std::shared_ptr<AnalysisRobot::Robot::RobotDriver>> m_robotDrivers;                         // 机器人驱动 (2个)
    std::shared_ptr<AnalysisRobot::PLC::PLCDriver> m_plcDriver;                                             // PLC驱动 (冲洗控制)
    
    // 设备控制器实例
    std::vector<std::shared_ptr<BalanceController>> m_balanceControllers;                                   // 天平控制器 (2个)
    std::vector<std::shared_ptr<HeatingMagneticStirrerController>> m_stirrerControllers;                   // 搅拌加热控制器 (11个)
    std::shared_ptr<MoistureAnalyzerController> m_moistureController;                                       // 水分测定仪控制器
    std::vector<std::shared_ptr<RobotController>> m_robotControllers;                                       // 机器人控制器 (2个)
    
    // 其他设备控制器 (各1个)
    std::shared_ptr<SampleEntryController> m_sampleEntryController;                                         // 进样传送控制器
    std::shared_ptr<SampleExitController> m_sampleExitController;                                           // 出样传送控制器
    std::shared_ptr<RepoController> m_repoController;                                                       // 容器货架控制器
    std::shared_ptr<DosingController> m_dosingController;                                                   // 加液控制器
    std::shared_ptr<VolumeController> m_volumeController;                                                   // 定容控制器
    std::shared_ptr<FilterController> m_filterController;                                                   // 过滤控制器
    std::shared_ptr<StirController> m_stirController;                                                       // 搅拌控制器
    std::shared_ptr<ICPEntryController> m_icpEntryController;                                               // ICP进样控制器
    std::shared_ptr<ShakerController> m_shakerController;                                                   // 摇床控制器
    std::shared_ptr<FrameController> m_frameController;                                                     // 框架控制器
    std::shared_ptr<PouringDeviceController> m_pouringController;                                                 // 倒样控制器
    
    // 设备控制器映射表 (用于REST接口)
    std::map<std::string, std::shared_ptr<AnalysisRobot::RestInterface::IDeviceController>> m_deviceControllers;
    
    // 设备配置
    std::vector<DeviceInfo> m_deviceConfigs;
    
    // 同步控制
    mutable std::mutex m_devicesMutex;
    
    // 状态信息
    std::string m_lastError;
    
    /**
     * @brief 初始化天平设备
     * @param config 配置信息
     * @return 是否成功
     */
    bool initializeBalances(const std::vector<DeviceConfig::BalanceDevice>& config);

    /**
     * @brief 初始化搅拌加热设备
     * @param config 配置信息
     * @return 是否成功
     */
    bool initializeStirrerHeaters(const std::vector<DeviceConfig::StirrerHeaterDevice>& config);
    
    /**
     * @brief 初始化水分测定仪
     * @param config 配置信息
     * @return 是否成功
     */
    bool initializeMoistureAnalyzer(const DeviceConfig::MoistureAnalyzerDevice& config);

    /**
     * @brief 初始化机器人
     * @param config 配置信息
     * @return 是否成功
     */
    bool initializeRobots(const std::vector<DeviceConfig::RobotDevice>& config);

    /**
     * @brief 初始化其他设备
     * @param config 配置信息
     * @return 是否成功
     */
    bool initializeOtherDevices(const DeviceConfig& config);
    
    /**
     * @brief 注册设备控制器
     * @param name 设备名称
     * @param controller 控制器实例
     */
    void registerDeviceController(const std::string& name, 
                                 std::shared_ptr<AnalysisRobot::RestInterface::IDeviceController> controller);
    
    /**
     * @brief 设置错误信息
     * @param error 错误信息
     */
    void setError(const std::string& error);
};

#endif // DEVICE_MANAGER_H
