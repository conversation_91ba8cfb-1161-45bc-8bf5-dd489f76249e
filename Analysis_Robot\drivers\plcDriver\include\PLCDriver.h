#ifndef PLC_DRIVER_H
#define PLC_DRIVER_H

#include <string>
#include <memory>
#include <functional>
#include <map>
#include <atomic>
#include <mutex>
#include <thread>
#include <modbus.h>

namespace AnalysisRobot {
namespace PLC {

/**
 * @brief PLC状态枚举
 */
enum class PLCStatus {
    DISCONNECTED = 0,   // 未连接
    CONNECTED = 1,      // 已连接
    RUNNING = 2,        // 运行中
    FAULT = 3,          // 故障
    MAINTENANCE = 4     // 维护模式
};

/**
 * @brief 设备类型枚举
 */
enum class DeviceType {
    // 传送系统
    SAMPLE_ENTRY_CONVEYOR = 1,  // 进样传送带
    SAMPLE_EXIT_CONVEYOR = 2,   // 出样传送带

    // 扫码开合盖装置
    SCANNER_CAP_DEVICE = 3,     // 扫码开合盖装置

    // 容器货架系统
    CONTAINER_RACK = 4,         // 容器货架
    STIRBAR_STORAGE = 5,        // 搅拌子存储装置

    // 过滤系统
    FILTER_DEVICE = 6,          // 过滤装置

    // 加液系统
    PUMP_SYSTEM = 7,            // 泵系统
    VALVE_CONTROLLER = 8,       // 阀门控制器

    // 安全系统
    SAFETY_DOOR = 9,            // 安全门
    EMERGENCY_STOP = 10,        // 急停按钮
    FRAME_LOCK = 11,            // 框架门锁

    // 环境控制
    VENTILATION_FAN = 12,       // 通风扇
    LIGHTING_SYSTEM = 13,       // 照明系统

    // 传感器系统
    SENSOR_ARRAY = 14,          // 传感器阵列

    // 冲洗系统
    FLUSH_SYSTEM = 15           // 冲洗系统
};

/**
 * @brief 设备状态结构
 */
struct DeviceStatus {
    DeviceType type;
    bool isOnline;
    bool isRunning;
    bool hasFault;
    std::string statusMessage;
    uint16_t rawStatus;
    
    DeviceStatus() : type(DeviceType::SAMPLE_ENTRY_CONVEYOR), isOnline(false),
                    isRunning(false), hasFault(false), rawStatus(0) {}
};

/**
 * @brief 传感器读数结构
 */
struct SensorReading {
    bool success;
    std::map<std::string, double> values;  // 传感器名称 -> 值
    std::string errorMsg;
    
    SensorReading() : success(false) {}
};

/**
 * @brief PLC配置结构
 */
struct PLCConfig {
    std::string ipAddress;      // PLC IP地址
    int port;                   // MODBUS TCP端口，默认502
    int responseTimeout;        // 响应超时时间（毫秒）
    int slaveId;               // 从机地址，默认1
    bool enableHeartbeat;       // 是否启用心跳
    int heartbeatInterval;      // 心跳间隔（毫秒）
    
    PLCConfig() 
        : ipAddress("*************")
        , port(502)
        , responseTimeout(1000)
        , slaveId(1)
        , enableHeartbeat(true)
        , heartbeatInterval(5000) {}
};

/**
 * @brief 状态回调函数类型
 */
using StatusCallback = std::function<void(PLCStatus status, const std::string& message)>;
using DeviceCallback = std::function<void(DeviceType device, const DeviceStatus& status)>;

/**
 * @brief PLC驱动类
 * 
 * 作为MODBUS TCP服务端，控制分析机器人系统中的各种外围设备：
 * - 进样/出样传送带系统
 * - 扫码开合盖装置
 * - 容器货架和搅拌子存储
 * - 过滤装置
 * - 加液系统（泵和阀门）
 * - 安全系统（门锁、急停）
 * - 环境控制（通风、照明）
 * - 传感器监控
 *
 * 注意：以下设备有独立驱动器，不通过PLC控制：
 * - 搅拌装置（独立驱动）
 * - 搅拌加热装置（HeatingMagneticStirrerDriver）
 * - ICP进样装置（独立驱动）
 * - 水分检测仪（MoistureAnalyzerDriver）
 * - 天平设备（BalanceDriver）
 * - 机械臂（RobotDriver）
 */
class PLCDriver {
public:
    /**
     * @brief 构造函数
     */
    PLCDriver();
    
    /**
     * @brief 析构函数
     */
    ~PLCDriver();
    
    /**
     * @brief 初始化PLC连接
     * @param config 配置参数
     * @return 是否成功
     */
    bool initialize(const PLCConfig& config);
    
    /**
     * @brief 启动MODBUS服务端
     * @return 是否成功
     */
    bool startServer();
    
    /**
     * @brief 停止服务端
     */
    void stopServer();
    
    /**
     * @brief 检查服务端状态
     * @return 是否正在运行
     */
    bool isRunning() const;

    /**
     * @brief 检查连接状态
     * @return 是否已连接
     */
    bool isConnected() const;
    
    // ========== 设备控制 ==========
    
    /**
     * @brief 启动设备
     * @param device 设备类型
     * @return 是否成功
     */
    bool startDevice(DeviceType device);
    
    /**
     * @brief 停止设备
     * @param device 设备类型
     * @return 是否成功
     */
    bool stopDevice(DeviceType device);
    
    /**
     * @brief 获取设备状态
     * @param device 设备类型
     * @return 设备状态
     */
    DeviceStatus getDeviceStatus(DeviceType device);
    
    /**
     * @brief 重置设备
     * @param device 设备类型
     * @return 是否成功
     */
    bool resetDevice(DeviceType device);
    
    // ========== 进样传送装置控制 ==========

    /**
     * @brief 检测来样
     * @return 是否检测到样品
     */
    bool checkSampleEntry();

    /**
     * @brief 扫码识别
     * @return 扫码结果，空字符串表示失败
     */
    std::string scanTag();

    /**
     * @brief 开盖操作
     * @return 是否成功
     */
    bool openCap();

    /**
     * @brief 关盖操作
     * @return 是否成功
     */
    bool closeCap();

    // ========== 出样传送装置控制 ==========

    /**
     * @brief 检查出样传送带状态
     * @return 传送带状态：IDLE空闲, FULL被占用
     */
    std::string checkSampleExitStatus();

    // ========== 容器货架控制 ==========

    /**
     * @brief 获取货架状态
     * @return 货架状态：HOMED原点, TURNED已旋转
     */
    std::string getRackStatus();

    /**
     * @brief 位置重定向
     * @param positionCode 原始位置编码
     * @return 重定向后的位置编码
     */
    std::string redirectPosition(const std::string& positionCode);

    /**
     * @brief 货架复位到原点
     * @return 是否成功
     */
    bool resetRack();

    /**
     * @brief 货架左转
     * @return 是否成功
     */
    bool turnRackLeft();

    /**
     * @brief 货架右转
     * @return 是否成功
     */
    bool turnRackRight();

    /**
     * @brief 放置搅拌子
     * @return 剩余搅拌子数量，-1表示失败
     */
    int placeStirBar();
    
    // ========== 过滤装置控制 ==========

    /**
     * @brief 安装过滤头
     * @return 是否成功
     */
    bool installFilter();

    /**
     * @brief 卸载过滤头
     * @return 是否成功
     */
    bool uninstallFilter();

    /**
     * @brief 执行过滤取样
     * @param volume 预期取样体积(ml)
     * @param actualVolume 实际取样体积(ml) [输出参数]
     * @param actualWeight 实际重量(g) [输出参数]
     * @param glassWeight 容器重量(g) [输出参数]
     * @return 是否成功
     */
    bool executeFilter(double volume, double& actualVolume, double& actualWeight, double& glassWeight);

    // ========== 加液系统控制 ==========

    /**
     * @brief 启动泵
     * @param pumpId 泵ID (1-4)
     * @param flowRate 流量 (ml/min)
     * @return 是否成功
     */
    bool startPump(int pumpId, double flowRate);

    /**
     * @brief 停止泵
     * @param pumpId 泵ID (1-4)
     * @return 是否成功
     */
    bool stopPump(int pumpId);

    /**
     * @brief 加液操作
     * @param solution 溶液类型 (EDTA, H2O, NH3)
     * @param volume 预期加液量(ml)
     * @param channel 通道号
     * @param actualVolume 实际加液量(ml) [输出参数]
     * @return 是否成功
     */
    bool executeDosing(const std::string& solution, double volume, int channel, double& actualVolume);

    /**
     * @brief 定容操作
     * @param volume 定容值(ml)
     * @param glassWeight 容器重量(g)
     * @param channel 加水通道
     * @param actualVolume 实际容量(ml) [输出参数]
     * @param actualWeight 实际重量(g) [输出参数]
     * @return 是否成功
     */
    bool executeVolume(double volume, double glassWeight, int channel, double& actualVolume, double& actualWeight);

    /**
     * @brief 设置阀门状态
     * @param valveId 阀门ID (1-8)
     * @param open 是否打开
     * @return 是否成功
     */
    bool setValveState(int valveId, bool open);

    /**
     * @brief 获取阀门状态
     * @param valveId 阀门ID (1-8)
     * @return 是否打开
     */
    bool getValveState(int valveId);
    
    // ========== 框架安全系统 ==========

    /**
     * @brief 检查门锁状态
     * @return 锁状态：LOCKED已锁定, UNLOCKED已解锁
     */
    std::string getFrameLockStatus();

    /**
     * @brief 门锁控制
     * @param lock true=锁定, false=解锁
     * @return 是否成功
     */
    bool setFrameLock(bool lock);

    /**
     * @brief 急停控制
     * @return 是否成功
     */
    bool urgentStop();

    /**
     * @brief 检查安全门状态
     * @return 是否关闭
     */
    bool isSafetyDoorClosed();

    /**
     * @brief 检查急停状态
     * @return 是否按下急停
     */
    bool isEmergencyStopPressed();

    /**
     * @brief 重置急停
     * @return 是否成功
     */
    bool resetEmergencyStop();

    // ========== 环境控制 ==========

    /**
     * @brief 设置通风扇速度
     * @param speed 速度百分比 (0-100)
     * @return 是否成功
     */
    bool setVentilationSpeed(int speed);

    /**
     * @brief 设置照明亮度
     * @param brightness 亮度百分比 (0-100)
     * @return 是否成功
     */
    bool setLightingBrightness(int brightness);
    
    // ========== 传感器监控 ==========
    
    /**
     * @brief 读取所有传感器数据
     * @return 传感器读数
     */
    SensorReading readAllSensors();
    
    /**
     * @brief 读取指定传感器
     * @param sensorName 传感器名称
     * @return 传感器值
     */
    double readSensor(const std::string& sensorName);

    // ========== 冲洗系统控制 ==========

    /**
     * @brief 执行温度传感器冲洗操作
     * @param heaterChannel 加热器通道 (0-10，对应heater1-heater11)
     * @param flushTimeout 冲洗时长(秒)
     * @return 是否成功
     */
    bool executeTemperatureSensorFlush(int heaterChannel, int flushTimeout);

    /**
     * @brief 停止温度传感器冲洗操作
     * @param heaterChannel 加热器通道 (0-10，对应heater1-heater11)
     * @return 是否成功
     */
    bool stopTemperatureSensorFlush(int heaterChannel);

    /**
     * @brief 获取温度传感器冲洗状态
     * @param heaterChannel 加热器通道 (0-10，对应heater1-heater11)
     * @return 是否正在冲洗
     */
    bool isTemperatureSensorFlushRunning(int heaterChannel);

    // ========== 低级寄存器操作 ==========

    /**
     * @brief 读取保持寄存器
     * @param address 寄存器地址
     * @param value 读取的值 [输出参数]
     * @return 是否成功
     */
    bool readHoldingRegister(int address, uint16_t& value);

    /**
     * @brief 写入保持寄存器
     * @param address 寄存器地址
     * @param value 写入的值
     * @return 是否成功
     */
    bool writeHoldingRegister(int address, uint16_t value);
    
    // ========== 状态和回调 ==========
    
    /**
     * @brief 获取PLC状态
     * @return 当前状态
     */
    PLCStatus getStatus() const;
    
    /**
     * @brief 设置状态回调
     * @param callback 状态回调函数
     */
    void setStatusCallback(StatusCallback callback);
    
    /**
     * @brief 设置设备状态回调
     * @param callback 设备状态回调函数
     */
    void setDeviceCallback(DeviceCallback callback);
    
    /**
     * @brief 获取最后错误信息
     * @return 错误信息
     */
    std::string getLastError() const;

private:
    modbus_t* m_modbusCtx;              // MODBUS上下文
    modbus_mapping_t* m_mapping;        // 寄存器映射
    PLCConfig m_config;                 // 配置参数
    PLCStatus m_status;                 // 当前状态
    StatusCallback m_statusCallback;    // 状态回调
    DeviceCallback m_deviceCallback;    // 设备回调
    std::string m_lastError;            // 最后错误信息
    
    // 线程管理
    std::atomic<bool> m_running;
    std::thread m_serverThread;
    std::thread m_heartbeatThread;
    std::mutex m_mutex;
    
    // 设备状态缓存
    std::map<DeviceType, DeviceStatus> m_deviceStates;
    
    /**
     * @brief 服务端主循环
     */
    void serverLoop();
    
    /**
     * @brief 心跳线程
     */
    void heartbeatLoop();
    
    /**
     * @brief 设置状态
     * @param status 新状态
     * @param message 状态消息
     */
    void setStatus(PLCStatus status, const std::string& message = "");
    
    /**
     * @brief 设置错误信息
     * @param error 错误信息
     */
    void setError(const std::string& error);
    
    /**
     * @brief 更新设备状态
     * @param device 设备类型
     * @param status 设备状态
     */
    void updateDeviceStatus(DeviceType device, const DeviceStatus& status);
    
    /**
     * @brief 获取设备寄存器地址
     * @param device 设备类型
     * @return 寄存器地址
     */
    int getDeviceRegisterAddress(DeviceType device);
    
    /**
     * @brief 写入设备寄存器
     * @param device 设备类型
     * @param value 写入值
     * @return 是否成功
     */
    bool writeDeviceRegister(DeviceType device, uint16_t value);
    
    /**
     * @brief 读取设备寄存器
     * @param device 设备类型
     * @return 寄存器值
     */
    uint16_t readDeviceRegister(DeviceType device);

    /**
     * @brief 初始化设备状态
     */
    void initializeDeviceStates();
};

} // namespace PLC
} // namespace AnalysisRobot

#endif // PLC_DRIVER_H
