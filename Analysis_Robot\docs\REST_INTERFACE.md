序号	设备	程序模块	指令	请求方法	URI	参数	响应	说明
                                
    进样传送装置	进样程序	来样检测	GET	/api/sampleEntry/query	"{taskId:1000,
action:""CHECK_SAMPLE""
}"	"{
taskId:1000,
action:""CHECK_SAMPLE"",
status:""SUCCESS"",    // FAILED, 未检测到样品来料
message:""扫码成功"",
updateTime:""2025-03-11 12:00:00""
}"	接口实现，当检测到传感器信号时，返回SUCCESS，否则返回FAILED,并提供错误原因
                                
    扫码开合盖装置	进样程序	扫码识别	POST	/api/sampleEntry/operation	"{taskId:1000,
action:""SCAN_TAG""
}"	"{
taskId:1000,
action:""SCAN_TAG"",
status:""SUCCESS"",    // FAILED, 未检测到样品来料
message:""校验成功"",
data: {
    tag: ""XXXX""
}
updateTime:""2025-03-11 12:00:00""
}"	接口实现，开启扫码器旋转，识别到TAG标签值，返回SUCCESS和tag值，否则返回FAILED,并提供错误原因
    扫码开合盖装置	倒样程序	开合盖	POST	/api/sampleEntry/operation	"{taskId:1000,
action:""OPEN_CAP"" // CLOSE_CAP
}"	"{
taskId:1000,
action:""OPEN_CAP"" // CLOSE_CAP
status:""SUCCESS"",    // FAILED, 
message:""开盖成功"",
updateTime:""2025-03-11 12:00:00""
}"	接口实现，执行开启或关闭瓶盖机构，等待执行完成后返回SUCCESS，否则返回FAILED,并提供错误原因
    出样传送装置	出样程序	传送带状态检测	GET	/api/sampleExit/query	"{taskId:1000,
action:""CHECK_STATUS""
}"	"{
taskId:1000,
action:""CHECK_STATUS"",
status:""IDLE"", // IDLE空闲, FULL被占用，FAILED,失败异常
message:""信号检测成功"",
updateTime:""2025-03-11 12:00:00""
}"	接口实现，检测出样传送带的传感器信号（对应机械臂放置点位）,有检测到物体返回FULL，未检测到物体返回IDLE,否则返回FAILED
                                
    容器货架装置	容器货架程序	获取容器货架设备状态	GET	/api/repo/status	无	"{
name:""容器货架装置"",
description:""描述设备信息"",
status:""HOMED"", // HOMED处于原点位置,TURNED已旋转,FAILED未能正确获取状态
message:""处于原点位置"",
updateTime:""2025-03-11 12:00:00""
}"	
        容器货架程序	货架点位重定向	POST	/api/repo/operation	"{taskId:1000,
action:""POS_REDIRECT"",
data: {
    positionCode: ""D:01"" // 待重定向的点位编码 D:11
}
}"	"{
taskId:1000,
action:""POS_REDIRECT"",
status:""SUCCESS"",    // FAILED, 失败原因
message:""重定向成功"",
data: {
    positionCode: ""A7"", // 待重定向的点位编码
    redirectedCode:  ""A0"" // 重定向后的点位编码
},
updateTime:""2025-03-11 12:00:00""
}"	"接口实现，提供原始点位与机器人可操作点位之间的映射关系，当该接口请求的点位不在机器人可操作点位区间时，旋转货架，使其位于机器人可操作区间，并返回重新定向之后的机器人操作点位；
若该接口请求点位在机器人可操作点位区间，则直接返回该点位；
 成功返回SUCCESS和重定向之后的点位值，否则返回FAILED,并提供错误原因"
        容器货架程序	货架人工操作指令	POST	/api/repo/operation	action:"RESET",// RESET复位旋转回原点, TURN_LEFT向左旋转一个单位,TURN_RIGHT向右旋转一个单位		RESET复位旋转回原点, TURN_LEFT向左旋转一个单位,TURN_RIGHT向右旋转一个单位(旋转30度或者60度)
    搅拌子存储装置	容器货架程序	放置搅拌子	POST	/api/repo/operation	"{taskId:1000,
action:""PLACE_STIRBAR""
}"	"{
taskId:1000,
action:""PLACE_STIRBAR"",
status:""SUCCESS"",    // FAILED, 失败原因
message:""放置搅拌子成功"",
data: {
    remainCount: 0, // 剩余搅拌子数量
},
updateTime:""2025-03-11 12:00:00""
}"	接口实现，开启搅拌子放置装置，成功返回SUCCESS和剩余搅拌子数量(升级功能，硬件无法识别，可暂不实现)，否则返回FAILED,并提供错误原因
                                
    工作台机械臂	机器人程序模块	获取设备状态	GET	/api/robot/status	无	"{
name:""工作台1机械臂"",
description:""描述设备信息"",
status:""IDLE"", // IDLE机械臂空闲可用,BUSY忙碌中,FAILED未获取状态
message:""设备空闲可用"",
data: {x:0,Y:0,z:0,u:0,v:0,w:0} // 机械臂坐标值，相对于台面
updateTime:""2025-03-11 12:00:00""
}"	返回当前机器人运行状态及当前点位信息
        机器人程序模块	提交机器人取放指令	POST	/api/robot/operation	"{taskId:1000,
action:""PICK_PLACE"",
data: {
    source: B01, // 源板位， 由复合机器人提供实际值
    destination: B02 // 目标点位，由复合机器人提供实际值
}
}"		实现该接口时，需要保持会话状态；
        机器人程序模块	查询机器人取放结果	GET	/api/robot/query	taskId=1000	"{
taskId:0,
action:""SUBMIT"",
status:""SUBMITTED"",
message:""任务提交成功"",
updateTime:""2025-03-11 12:00:00""
}"	接口的调用方会通过轮询的方式，查看是否结束：返回status字段为SUCCESS或者FAILED
                                
    倒样称量装置	倒样程序	获取倒样设备状态	GET	/api/sampleBalance/status	无		
        倒样程序	称量天平复位清零	POST	/api/sampleBalance/operation	action="RESET"		实现天平的复位清零
        倒样程序	提交倒样称量指令	POST	/api/sampleBalance/operation	"{taskId=1000,
action:""WEIGH"",
data: {
    container: ""GLASS"",// GLASS烧杯，PETRI培养皿
    meassureValue: 1 // 预期称量值(g)
}
}"		工作站实现倒料装置与天平称量的配合，完成后返回计量结果, 返回结果中需要包含样品实际称量值和容器的重量
        倒样程序	查询倒样称量结果	GET	/api/sampleBalance/query	taskId=1000	"data: {
    meassureValue: 1,
    actualValue:0.9981, // 实际称重量,单位g
    glassWeight: 200.009, // 容器重量(包括搅拌子),单位g
}"	
        通用称量模块	提交称量指令	POST	/api/balance/operation	"{taskId=1000,
action:""WEIGH""
}"		接口实现时，直接获取天平结果，完成后返回计量结果
        通用称量模块	查询称量结果	GET	/api/balance/query	taskId=1000	"data: {
    actualValue:0.9981, // 当status为SUCCESS时，该字段有效
}"	
                                
    加液称量装置	通用称量模块	获取加液称量设备状态	GET	/api/balance/status			
        通用称量模块	称量清零	POST	/api/balance/operation	"{taskId=1000,
action:""RESET""
}"		接口实现时，直接获取天平结果，完成后返回计量结果
        通用加液模块	加液EDTA/纯水/硝酸	POST	/api/dosing/operation	"action:""DOSING"",
data: {
    solution: ""EDTA"",//溶液类型NH3,H2O,EDTA
    meassureValue: 15,//预期加液容量ml
    channel:0
}"		接口实现，依据参数待加入量，自主控制启停
        通用加液模块	查询加液结果	GET	/api/dosing/query	taskId=1000	"data: {
    solution: ""EDTA"",//溶液类型NH3,H2O,EDTA
    meassureValue: 15,//预期加液容量ml
    actualValue:14.99 // 当status为SUCCESS时，该字段有效
},"	返回实际加入量，已便于管理端软件统计物料使用情况
        定容程序	提交定容指令	POST	/api/volume/operation	"{
taskId:0,
action:""VOLUME"",
data: {
    volume: 250,//定容值(ml)
    channel:0, // 加纯水通道,预留
  glassWeight:190// // 烧杯容器重量(包括搅拌子)
}
}"		参数glassWeight数值来源于前置步骤得到的容器重量，该接口实现需要定容的volume数值250，在计量结果时，可以使用glassWeight+volume的结果来判定是否定容完成
        定容程序	查询定容结果	GET	/api/volume/query		"data: {
    volume: 250,//定容值(ml)
    actualVolume:250, //实际容量(ml)
    actualWeight: 294 //实际重量(g)
},"	
                                
    过滤装置	过滤程序	装卸过滤头指令	POST	/api/filter/operation	action:"INSTALL_FILTER"//UNINSTALL_FILTER		
        过滤程序	提交过滤取样指令	POST	/api/filter/operation	"action:""FILTER"",
data: {
    volume: 10//预期加液容量ml
}"		接口实现:1.获取容器称量值,2.放入吸液针,3.开启蠕动泵同时监控计量结果,4.到达预定值后关闭蠕动泵,返回计量值和容器重量, 5.吸液针返回清水槽
        过滤程序	查询过滤取样结果	GET	/api/filter/query		"data: {
    volume: 10,//定容值(ml)
    actualVolume:9.50, //实际容量(ml)
    actualWeight:9.55, // 实际重量(g)
    glassWeight:190 // 烧杯容器重量(包括搅拌子)
},"	
    搅拌装置	搅拌程序	搅拌	POST	/api/stir/operation	action:"START_STIR",// START_STIR/STOP_STIR 开启和停止搅拌		
                                
    搅拌加热装置	搅拌加热程序	获取加热设备状态	GET	/api/heater/status	无		
 搅拌加热程序	开启加热搅拌	POST	/api/heater/operation  ；；        action:"START",// START/STOP 开启和停止搅拌
,data:{
  channel:0,           // 选取加热设备及取液通道标识
  temperature:60, // 设定加热温度(单位:摄氏度)
  stirSpeed:1000,  // 搅拌速度(单位:转/分钟)
  flushTimeout:10 // 冲洗时长(单位:秒),当action="STOP"有效
}
                                
    ICP进样装置	ICP进样程序	开启ICP进液	POST	/api/icpEntry/operation	"action:""START"",// START/STOP 开启和停止ICP进样
data: {
    channel: 0, //进液通道, 0:纯水管路,1 2 3 ...n:标液管路, n+1: 样品管路
  type:""BLANK""// BLANK:空白纯水管路,STANDARD:标液管路,SAMPLE:样品管路
}"		"接口实现: 当type为SAMPLE时，多通阀切换到样品通路，若action=""START""，则把吸液针从清水池移至烧杯溶液中，“STOP”则把吸液针从烧杯溶液移回到清水池;
当type为BLANK或STANDARD时多通阀切换到对应通路即可"
                GET	/api/icpEntry/query			
                                
    水分检测仪器	水分检测程序	查询水分检测仪设备状态	GET	/api/moistureBalance/status	无	"{
name:""水分检测仪"",
description:""描述设备信息"",
status:""SUCCESS"", //设备状态正常,FAILED状态检测失败
message:""设备状态正常"",
updateTime:""2025-03-11 12:00:00""
}"	检测仪器通讯状态,可连接返回SUCCESS,否则返回FAILED,并给出失败原因
        水分检测程序	清零,去皮,开关盖,加热	POST	/api/moistureBalance/operation	action:"ZERO",// ZERO清零, BARE去皮,OPEN开盖,CLOSE关盖,HEAT加热	status:"SUCCESS",    // FAILED, 失败原因	透传仪器指令，需等待指令正确返回后才返回API结果:SUCCESS指令执行完成，FAILED执行失败，并给出失败原因
        水分检测程序	称重	POST	/api/moistureBalance/operation	action:"WEIGHT"	"data: {
weight:""5g""
}"	调用仪器称重指令获取称量结果，单位g
        水分检测程序	获取检测结果	GET	/api/moistureBalance/query	"{taskId=1000,
action:""GET_RESULT""
}"	"data: {
weight:""4.3g"",
mc:""35%""
}"	调用仪器检测结果，返回weight干燥后的实际重量, mc水分百分比含量
    培养皿摇床	水分检测程序	开关摇床	POST	/api/shaker/operation	action:"START" //开启，STOP停止		
                                
    框架装置	框架控制程序	查询锁状态	GET	/api/frame/status	无	"{
name:""框架装置"",
description:""描述设备信息"",
status:""UNLOCKED"", // LOCKED门框已被锁定,UNLOCKED门框已被解锁,FAILED状态检测失败
message:""门框已被锁定"",
updateTime:""2025-03-11 12:00:00""
}"	门锁状态检测实现
        框架控制程序	门锁控制	POST	/api/frame/operation	action:"UNLOCK",//LOCK		实现门框解锁
        框架控制程序	急停控制	POST	/api/frame/operation	action:"URGENT_STOP",		实现急停所有机械臂运动，并开锁门框
