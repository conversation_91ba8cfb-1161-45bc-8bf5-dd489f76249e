#include "PLCDriver.h"
#include "glog.h"
#include <sstream>
#include <cstring>
#include <errno.h>
#include <thread>
#include <chrono>

namespace AnalysisRobot {
namespace PLC {

// 寄存器地址映射
namespace RegisterMap {
    // 设备控制寄存器 (0x0000-0x000F)
    constexpr int SAMPLE_ENTRY_CONVEYOR = 0x0000;
    constexpr int SAMPLE_EXIT_CONVEYOR = 0x0001;
    constexpr int SCANNER_CAP_DEVICE = 0x0002;
    constexpr int CONTAINER_RACK = 0x0003;
    constexpr int STIRBAR_STORAGE = 0x0004;
    constexpr int FILTER_DEVICE = 0x0005;
    constexpr int PUMP_SYSTEM = 0x0006;
    constexpr int VALVE_CONTROLLER = 0x0007;
    constexpr int SAFETY_DOOR = 0x0008;
    constexpr int EMERGENCY_STOP = 0x0009;
    constexpr int FRAME_LOCK = 0x000A;
    constexpr int VENTILATION_FAN = 0x000B;
    constexpr int LIGHTING_SYSTEM = 0x000C;
    constexpr int SENSOR_ARRAY = 0x000D;

    // 进样传送装置控制 (0x0010-0x001F)
    constexpr int SAMPLE_ENTRY_SENSOR = 0x0010;     // 来样检测传感器
    constexpr int SCANNER_CONTROL = 0x0011;         // 扫码器控制
    constexpr int SCANNER_RESULT = 0x0012;          // 扫码结果寄存器
    constexpr int CAP_CONTROL = 0x0013;             // 开合盖控制
    constexpr int CAP_STATUS = 0x0014;              // 开合盖状态

    // 出样传送装置控制 (0x0020-0x002F)
    constexpr int SAMPLE_EXIT_SENSOR = 0x0020;      // 出样检测传感器

    // 容器货架控制 (0x0030-0x003F)
    constexpr int RACK_STATUS = 0x0030;             // 货架状态
    constexpr int RACK_CONTROL = 0x0031;            // 货架控制指令
    constexpr int RACK_POSITION = 0x0032;           // 货架当前位置
    constexpr int STIRBAR_COUNT = 0x0033;           // 搅拌子数量

    // 过滤装置控制 (0x0040-0x004F)
    constexpr int FILTER_HEAD_CONTROL = 0x0040;     // 过滤头控制
    constexpr int FILTER_HEAD_STATUS = 0x0041;      // 过滤头状态
    constexpr int FILTER_VOLUME_CMD = 0x0042;       // 过滤体积指令
    constexpr int FILTER_VOLUME_ACTUAL = 0x0043;    // 实际过滤体积

    // 泵系统控制 (0x0050-0x005F)
    constexpr int PUMP1_CONTROL = 0x0050;
    constexpr int PUMP2_CONTROL = 0x0051;
    constexpr int PUMP3_CONTROL = 0x0052;
    constexpr int PUMP4_CONTROL = 0x0053;
    constexpr int PUMP1_FLOWRATE = 0x0054;
    constexpr int PUMP2_FLOWRATE = 0x0055;
    constexpr int PUMP3_FLOWRATE = 0x0056;
    constexpr int PUMP4_FLOWRATE = 0x0057;

    // 阀门控制 (0x0060-0x006F)
    constexpr int VALVE_STATES = 0x0060;            // 8个阀门状态位

    // 框架安全系统 (0x0070-0x007F)
    constexpr int FRAME_LOCK_STATUS = 0x0070;       // 门锁状态
    constexpr int FRAME_LOCK_CONTROL = 0x0071;      // 门锁控制
    constexpr int SAFETY_STATUS = 0x0072;           // 安全状态
    constexpr int EMERGENCY_RESET = 0x0073;         // 急停重置

    // 环境控制 (0x0080-0x008F)
    constexpr int VENTILATION_SPEED = 0x0080;
    constexpr int LIGHTING_BRIGHTNESS = 0x0081;

    // 传感器数据 (0x0090-0x009F) - 输入寄存器
    constexpr int TEMPERATURE_SENSOR = 0x0090;
    constexpr int HUMIDITY_SENSOR = 0x0091;
    constexpr int PRESSURE_SENSOR = 0x0092;
    constexpr int FLOW_SENSOR = 0x0093;
    constexpr int LEVEL_SENSOR = 0x0094;
    constexpr int PH_SENSOR = 0x0095;

    // 冲洗系统控制 (0x00A0-0x00BF) - 支持11个加热器通道
    constexpr int FLUSH_CONTROL_BASE = 0x00A0;      // 冲洗控制基地址 (0x00A0-0x00AA)
    constexpr int FLUSH_TIMEOUT_BASE = 0x00AB;      // 冲洗时间基地址 (0x00AB-0x00B5)
    constexpr int FLUSH_STATUS_BASE = 0x00B6;       // 冲洗状态基地址 (0x00B6-0x00C0)

    // 系统状态 (0x00C1-0x00CF)
    constexpr int SYSTEM_STATUS = 0x00C1;
    constexpr int HEARTBEAT = 0x00C2;
}

PLCDriver::PLCDriver()
    : m_modbusCtx(nullptr)
    , m_mapping(nullptr)
    , m_status(PLCStatus::DISCONNECTED)
    , m_statusCallback(nullptr)
    , m_deviceCallback(nullptr)
    , m_running(false) {
}

PLCDriver::~PLCDriver() {
    stopServer();
}

bool PLCDriver::initialize(const PLCConfig& config) {
    m_config = config;
    
    // 创建MODBUS TCP服务端上下文
    m_modbusCtx = modbus_new_tcp(m_config.ipAddress.c_str(), m_config.port);
    if (m_modbusCtx == nullptr) {
        setError("Failed to create MODBUS context: " + std::string(modbus_strerror(errno)));
        return false;
    }
    
    // 设置从机地址
    if (modbus_set_slave(m_modbusCtx, m_config.slaveId) == -1) {
        setError("Failed to set slave ID: " + std::string(modbus_strerror(errno)));
        modbus_free(m_modbusCtx);
        m_modbusCtx = nullptr;
        return false;
    }
    
    // 设置响应超时
    struct timeval timeout;
    timeout.tv_sec = m_config.responseTimeout / 1000;
    timeout.tv_usec = (m_config.responseTimeout % 1000) * 1000;
    modbus_set_response_timeout(m_modbusCtx, timeout.tv_sec, timeout.tv_usec);
    
    // 创建寄存器映射 (保持寄存器: 100个, 输入寄存器: 100个)
    m_mapping = modbus_mapping_new(0, 0, 100, 100);
    if (m_mapping == nullptr) {
        setError("Failed to create register mapping: " + std::string(modbus_strerror(errno)));
        modbus_free(m_modbusCtx);
        m_modbusCtx = nullptr;
        return false;
    }
    
    // 初始化设备状态
    initializeDeviceStates();
    
    setStatus(PLCStatus::CONNECTED, "PLC driver initialized successfully");
    return true;
}

bool PLCDriver::startServer() {
    if (m_modbusCtx == nullptr) {
        setError("PLC not initialized");
        return false;
    }
    
    if (m_running.load()) {
        setError("Server already running");
        return false;
    }
    
    // 监听连接
    if (modbus_tcp_listen(m_modbusCtx, 1) == -1) {
        setError("Failed to listen: " + std::string(modbus_strerror(errno)));
        return false;
    }
    
    m_running.store(true);
    
    // 启动服务端线程
    m_serverThread = std::thread(&PLCDriver::serverLoop, this);
    
    // 启动心跳线程
    if (m_config.enableHeartbeat) {
        m_heartbeatThread = std::thread(&PLCDriver::heartbeatLoop, this);
    }
    
    setStatus(PLCStatus::RUNNING, "PLC server started successfully");
    return true;
}

void PLCDriver::stopServer() {
    if (!m_running.load()) {
        return;
    }
    
    m_running.store(false);
    
    // 等待线程结束
    if (m_serverThread.joinable()) {
        m_serverThread.join();
    }
    
    if (m_heartbeatThread.joinable()) {
        m_heartbeatThread.join();
    }
    
    // 清理资源
    if (m_mapping) {
        modbus_mapping_free(m_mapping);
        m_mapping = nullptr;
    }
    
    if (m_modbusCtx) {
        modbus_close(m_modbusCtx);
        modbus_free(m_modbusCtx);
        m_modbusCtx = nullptr;
    }
    
    setStatus(PLCStatus::DISCONNECTED, "PLC server stopped");
}

bool PLCDriver::isRunning() const {
    return m_running.load();
}

bool PLCDriver::isConnected() const {
    return isRunning() && m_modbusCtx != nullptr;
}

bool PLCDriver::startDevice(DeviceType device) {
    if (!isRunning()) {
        setError("PLC server not running");
        return false;
    }
    
    return writeDeviceRegister(device, 1);  // 1 = 启动
}

bool PLCDriver::stopDevice(DeviceType device) {
    if (!isRunning()) {
        setError("PLC server not running");
        return false;
    }
    
    return writeDeviceRegister(device, 0);  // 0 = 停止
}

DeviceStatus PLCDriver::getDeviceStatus(DeviceType device) {
    std::lock_guard<std::mutex> lock(m_mutex);
    auto it = m_deviceStates.find(device);
    if (it != m_deviceStates.end()) {
        return it->second;
    }
    
    DeviceStatus status;
    status.type = device;
    return status;
}

bool PLCDriver::resetDevice(DeviceType device) {
    if (!isRunning()) {
        setError("PLC server not running");
        return false;
    }
    
    return writeDeviceRegister(device, 2);  // 2 = 重置
}

// ========== 进样传送装置控制 ==========

bool PLCDriver::checkSampleEntry() {
    if (!isRunning()) {
        setError("PLC server not running");
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_mapping) {
        uint16_t sensorValue = m_mapping->tab_input_registers[RegisterMap::SAMPLE_ENTRY_SENSOR - 0x0090];
        return sensorValue > 0;  // 检测到样品
    }
    return false;
}

std::string PLCDriver::scanTag() {
    if (!isRunning()) {
        setError("PLC server not running");
        return "";
    }

    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_mapping) {
        // 启动扫码器
        m_mapping->tab_registers[RegisterMap::SCANNER_CONTROL] = 1;

        // 模拟扫码结果（实际应该从设备读取）
        uint16_t scanResult = m_mapping->tab_registers[RegisterMap::SCANNER_RESULT];
        if (scanResult > 0) {
            return "TAG_" + std::to_string(scanResult);
        }
    }
    return "";
}

bool PLCDriver::openCap() {
    if (!isRunning()) {
        setError("PLC server not running");
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_mapping) {
        m_mapping->tab_registers[RegisterMap::CAP_CONTROL] = 1;  // 1 = 开盖
        return true;
    }
    return false;
}

bool PLCDriver::closeCap() {
    if (!isRunning()) {
        setError("PLC server not running");
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_mapping) {
        m_mapping->tab_registers[RegisterMap::CAP_CONTROL] = 0;  // 0 = 关盖
        return true;
    }
    return false;
}

// ========== 出样传送装置控制 ==========

std::string PLCDriver::checkSampleExitStatus() {
    if (!isRunning()) {
        setError("PLC server not running");
        return "FAILED";
    }

    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_mapping) {
        uint16_t sensorValue = m_mapping->tab_input_registers[RegisterMap::SAMPLE_EXIT_SENSOR - 0x0090];
        return (sensorValue > 0) ? "FULL" : "IDLE";
    }
    return "FAILED";
}

// ========== 容器货架控制 ==========

std::string PLCDriver::getRackStatus() {
    if (!isRunning()) {
        setError("PLC server not running");
        return "FAILED";
    }

    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_mapping) {
        uint16_t status = m_mapping->tab_registers[RegisterMap::RACK_STATUS];
        return (status == 0) ? "HOMED" : "TURNED";
    }
    return "FAILED";
}

std::string PLCDriver::redirectPosition(const std::string& positionCode) {
    // 简单的位置重定向逻辑（实际应该根据货架状态计算）
    if (positionCode.substr(0, 2) == "D:") {
        // D:01 -> A7, D:11 -> A0 等
        return "A" + std::to_string(std::stoi(positionCode.substr(2)) % 8);
    }
    return positionCode;  // 如果已经在可操作区间，直接返回
}

bool PLCDriver::resetRack() {
    if (!isRunning()) {
        setError("PLC server not running");
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_mapping) {
        m_mapping->tab_registers[RegisterMap::RACK_CONTROL] = 0;  // 0 = 复位
        return true;
    }
    return false;
}

bool PLCDriver::turnRackLeft() {
    if (!isRunning()) {
        setError("PLC server not running");
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_mapping) {
        m_mapping->tab_registers[RegisterMap::RACK_CONTROL] = 1;  // 1 = 左转
        return true;
    }
    return false;
}

bool PLCDriver::turnRackRight() {
    if (!isRunning()) {
        setError("PLC server not running");
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_mapping) {
        m_mapping->tab_registers[RegisterMap::RACK_CONTROL] = 2;  // 2 = 右转
        return true;
    }
    return false;
}

int PLCDriver::placeStirBar() {
    if (!isRunning()) {
        setError("PLC server not running");
        return -1;
    }

    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_mapping) {
        // 执行放置搅拌子操作
        writeDeviceRegister(DeviceType::STIRBAR_STORAGE, 1);

        // 返回剩余搅拌子数量
        uint16_t count = m_mapping->tab_registers[RegisterMap::STIRBAR_COUNT];
        return static_cast<int>(count);
    }
    return -1;
}

bool PLCDriver::startPump(int pumpId, double flowRate) {
    if (pumpId < 1 || pumpId > 4) {
        setError("Invalid pump ID: " + std::to_string(pumpId));
        return false;
    }
    
    if (flowRate < 0 || flowRate > 1000) {
        setError("Invalid flow rate: " + std::to_string(flowRate));
        return false;
    }
    
    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_mapping) {
        int controlReg = RegisterMap::PUMP1_CONTROL + (pumpId - 1);
        int flowReg = RegisterMap::PUMP1_FLOWRATE + (pumpId - 1);
        
        m_mapping->tab_registers[controlReg] = 1;  // 启动
        m_mapping->tab_registers[flowReg] = static_cast<uint16_t>(flowRate);
        return true;
    }
    return false;
}

bool PLCDriver::stopPump(int pumpId) {
    if (pumpId < 1 || pumpId > 4) {
        setError("Invalid pump ID: " + std::to_string(pumpId));
        return false;
    }
    
    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_mapping) {
        int controlReg = RegisterMap::PUMP1_CONTROL + (pumpId - 1);
        m_mapping->tab_registers[controlReg] = 0;  // 停止
        return true;
    }
    return false;
}

bool PLCDriver::executeDosing(const std::string& solution, double volume, int channel, double& actualVolume) {
    if (!isRunning()) {
        setError("PLC server not running");
        return false;
    }

    if (volume <= 0 || volume > 1000) {
        setError("Invalid dosing volume: " + std::to_string(volume));
        return false;
    }

    if (channel < 0 || channel > 3) {
        setError("Invalid dosing channel: " + std::to_string(channel));
        return false;
    }

    // 验证溶液类型
    if (solution != "EDTA" && solution != "H2O" && solution != "NH3") {
        setError("Invalid solution type: " + solution);
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_mapping) {
        // 启动对应的泵
        int pumpReg = RegisterMap::PUMP1_CONTROL + channel;
        int flowReg = RegisterMap::PUMP1_FLOWRATE + channel;

        m_mapping->tab_registers[pumpReg] = 1;  // 启动泵
        m_mapping->tab_registers[flowReg] = static_cast<uint16_t>(volume * 10);  // 转换为0.1ml单位

        // 模拟加液过程（实际应该等待设备完成）
        actualVolume = volume * 0.999;  // 99.9%精度

        return true;
    }
    return false;
}

bool PLCDriver::executeVolume(double volume, double glassWeight, int channel, double& actualVolume, double& actualWeight) {
    if (!isRunning()) {
        setError("PLC server not running");
        return false;
    }

    if (volume <= 0 || volume > 1000) {
        setError("Invalid volume: " + std::to_string(volume));
        return false;
    }

    if (glassWeight <= 0) {
        setError("Invalid glass weight: " + std::to_string(glassWeight));
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_mapping) {
        // 启动加水泵进行定容
        int pumpReg = RegisterMap::PUMP1_CONTROL + channel;
        int flowReg = RegisterMap::PUMP1_FLOWRATE + channel;

        m_mapping->tab_registers[pumpReg] = 1;  // 启动泵
        m_mapping->tab_registers[flowReg] = static_cast<uint16_t>(volume * 10);  // 转换为0.1ml单位

        // 模拟定容过程
        actualVolume = volume;
        actualWeight = glassWeight + volume;  // 假设水的密度为1g/ml

        return true;
    }
    return false;
}

// ========== 过滤装置控制 ==========

bool PLCDriver::installFilter() {
    if (!isRunning()) {
        setError("PLC server not running");
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_mapping) {
        m_mapping->tab_registers[RegisterMap::FILTER_HEAD_CONTROL] = 1;  // 1 = 安装
        return true;
    }
    return false;
}

bool PLCDriver::uninstallFilter() {
    if (!isRunning()) {
        setError("PLC server not running");
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_mapping) {
        m_mapping->tab_registers[RegisterMap::FILTER_HEAD_CONTROL] = 0;  // 0 = 卸载
        return true;
    }
    return false;
}

bool PLCDriver::executeFilter(double volume, double& actualVolume, double& actualWeight, double& glassWeight) {
    if (!isRunning()) {
        setError("PLC server not running");
        return false;
    }

    if (volume <= 0 || volume > 100) {
        setError("Invalid filter volume: " + std::to_string(volume));
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_mapping) {
        // 设置过滤体积指令
        m_mapping->tab_registers[RegisterMap::FILTER_VOLUME_CMD] = static_cast<uint16_t>(volume * 100);  // 转换为0.01ml单位

        // 模拟过滤过程（实际应该等待设备完成）
        // 这里简化处理，直接返回模拟结果
        actualVolume = volume * 0.95;  // 95%效率
        actualWeight = actualVolume * 1.05;  // 假设密度略大于水
        glassWeight = 190.0;  // 固定容器重量

        // 更新实际过滤体积寄存器
        m_mapping->tab_registers[RegisterMap::FILTER_VOLUME_ACTUAL] = static_cast<uint16_t>(actualVolume * 100);

        return true;
    }
    return false;
}

bool PLCDriver::setValveState(int valveId, bool open) {
    if (valveId < 1 || valveId > 8) {
        setError("Invalid valve ID: " + std::to_string(valveId));
        return false;
    }
    
    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_mapping) {
        uint16_t& valveStates = m_mapping->tab_registers[RegisterMap::VALVE_STATES];
        int bitPos = valveId - 1;
        
        if (open) {
            valveStates |= (1 << bitPos);   // 设置位
        } else {
            valveStates &= ~(1 << bitPos);  // 清除位
        }
        return true;
    }
    return false;
}

bool PLCDriver::getValveState(int valveId) {
    if (valveId < 1 || valveId > 8) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_mapping) {
        uint16_t valveStates = m_mapping->tab_registers[RegisterMap::VALVE_STATES];
        int bitPos = valveId - 1;
        return (valveStates & (1 << bitPos)) != 0;
    }
    return false;
}

// ========== 框架安全系统 ==========

std::string PLCDriver::getFrameLockStatus() {
    if (!isRunning()) {
        setError("PLC server not running");
        return "FAILED";
    }

    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_mapping) {
        uint16_t lockStatus = m_mapping->tab_registers[RegisterMap::FRAME_LOCK_STATUS];
        return (lockStatus == 1) ? "LOCKED" : "UNLOCKED";
    }
    return "FAILED";
}

bool PLCDriver::setFrameLock(bool lock) {
    if (!isRunning()) {
        setError("PLC server not running");
        return false;
    }

    std::lock_guard<std::mutex> lock_guard(m_mutex);
    if (m_mapping) {
        m_mapping->tab_registers[RegisterMap::FRAME_LOCK_CONTROL] = lock ? 1 : 0;
        m_mapping->tab_registers[RegisterMap::FRAME_LOCK_STATUS] = lock ? 1 : 0;
        return true;
    }
    return false;
}

bool PLCDriver::urgentStop() {
    if (!isRunning()) {
        setError("PLC server not running");
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_mapping) {
        // 急停所有设备
        m_mapping->tab_registers[RegisterMap::EMERGENCY_STOP] = 1;
        // 同时解锁门框
        m_mapping->tab_registers[RegisterMap::FRAME_LOCK_CONTROL] = 0;
        m_mapping->tab_registers[RegisterMap::FRAME_LOCK_STATUS] = 0;
        return true;
    }
    return false;
}

bool PLCDriver::isSafetyDoorClosed() {
    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_mapping) {
        uint16_t safetyStatus = m_mapping->tab_registers[RegisterMap::SAFETY_STATUS];
        return (safetyStatus & 0x0001) != 0;  // 位0：安全门状态
    }
    return false;
}

bool PLCDriver::isEmergencyStopPressed() {
    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_mapping) {
        uint16_t safetyStatus = m_mapping->tab_registers[RegisterMap::SAFETY_STATUS];
        return (safetyStatus & 0x0002) != 0;  // 位1：急停状态
    }
    return false;
}

bool PLCDriver::resetEmergencyStop() {
    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_mapping) {
        m_mapping->tab_registers[RegisterMap::EMERGENCY_RESET] = 1;
        return true;
    }
    return false;
}

bool PLCDriver::setVentilationSpeed(int speed) {
    if (speed < 0 || speed > 100) {
        setError("Invalid ventilation speed: " + std::to_string(speed));
        return false;
    }
    
    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_mapping) {
        m_mapping->tab_registers[RegisterMap::VENTILATION_SPEED] = static_cast<uint16_t>(speed);
        return true;
    }
    return false;
}

bool PLCDriver::setLightingBrightness(int brightness) {
    if (brightness < 0 || brightness > 100) {
        setError("Invalid lighting brightness: " + std::to_string(brightness));
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_mapping) {
        m_mapping->tab_registers[RegisterMap::LIGHTING_BRIGHTNESS] = static_cast<uint16_t>(brightness);
        return true;
    }
    return false;
}

SensorReading PLCDriver::readAllSensors() {
    SensorReading result;

    if (!isRunning()) {
        result.errorMsg = "PLC server not running";
        return result;
    }

    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_mapping) {
        result.values["temperature"] = static_cast<double>(m_mapping->tab_input_registers[RegisterMap::TEMPERATURE_SENSOR - 0x0070]) / 10.0;
        result.values["humidity"] = static_cast<double>(m_mapping->tab_input_registers[RegisterMap::HUMIDITY_SENSOR - 0x0070]) / 10.0;
        result.values["pressure"] = static_cast<double>(m_mapping->tab_input_registers[RegisterMap::PRESSURE_SENSOR - 0x0070]) / 100.0;
        result.values["flow"] = static_cast<double>(m_mapping->tab_input_registers[RegisterMap::FLOW_SENSOR - 0x0070]) / 10.0;
        result.values["level"] = static_cast<double>(m_mapping->tab_input_registers[RegisterMap::LEVEL_SENSOR - 0x0070]) / 10.0;
        result.values["ph"] = static_cast<double>(m_mapping->tab_input_registers[RegisterMap::PH_SENSOR - 0x0070]) / 100.0;
        result.success = true;
    } else {
        result.errorMsg = "Register mapping not available";
    }

    return result;
}

double PLCDriver::readSensor(const std::string& sensorName) {
    auto reading = readAllSensors();
    if (reading.success && reading.values.find(sensorName) != reading.values.end()) {
        return reading.values[sensorName];
    }
    return 0.0;
}

PLCStatus PLCDriver::getStatus() const {
    return m_status;
}

void PLCDriver::setStatusCallback(StatusCallback callback) {
    m_statusCallback = callback;
}

void PLCDriver::setDeviceCallback(DeviceCallback callback) {
    m_deviceCallback = callback;
}

std::string PLCDriver::getLastError() const {
    return m_lastError;
}

void PLCDriver::serverLoop() {
    uint8_t query[MODBUS_TCP_MAX_ADU_LENGTH];

    while (m_running.load()) {
        // 接受客户端连接
        int socket = modbus_tcp_accept(m_modbusCtx, nullptr);
        if (socket == -1) {
            if (m_running.load()) {
                setError("Failed to accept connection: " + std::string(modbus_strerror(errno)));
            }
            continue;
        }

        setStatus(PLCStatus::RUNNING, "Client connected");

        // 处理客户端请求
        while (m_running.load()) {
            int rc = modbus_receive(m_modbusCtx, query);
            if (rc > 0) {
                modbus_reply(m_modbusCtx, query, rc, m_mapping);
            } else if (rc == -1) {
                break;  // 连接断开
            }
        }

        modbus_close(m_modbusCtx);
        setStatus(PLCStatus::CONNECTED, "Client disconnected");
    }
}

void PLCDriver::heartbeatLoop() {
    uint16_t heartbeatCounter = 0;

    while (m_running.load()) {
        std::this_thread::sleep_for(std::chrono::milliseconds(m_config.heartbeatInterval));

        if (m_mapping) {
            std::lock_guard<std::mutex> lock(m_mutex);
            m_mapping->tab_registers[RegisterMap::HEARTBEAT] = heartbeatCounter++;
        }
    }
}

void PLCDriver::setStatus(PLCStatus status, const std::string& message) {
    m_status = status;
    if (m_statusCallback) {
        m_statusCallback(status, message);
    }

    // 更新系统状态寄存器
    if (m_mapping) {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_mapping->tab_registers[RegisterMap::SYSTEM_STATUS] = static_cast<uint16_t>(status);
    }
}

void PLCDriver::setError(const std::string& error) {
    m_lastError = error;
    LOG(ERROR) << "PLCDriver Error: " << error;
}

void PLCDriver::updateDeviceStatus(DeviceType device, const DeviceStatus& status) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_deviceStates[device] = status;

    if (m_deviceCallback) {
        m_deviceCallback(device, status);
    }
}

int PLCDriver::getDeviceRegisterAddress(DeviceType device) {
    switch (device) {
        case DeviceType::SAMPLE_ENTRY_CONVEYOR: return RegisterMap::SAMPLE_ENTRY_CONVEYOR;
        case DeviceType::SAMPLE_EXIT_CONVEYOR: return RegisterMap::SAMPLE_EXIT_CONVEYOR;
        case DeviceType::SCANNER_CAP_DEVICE: return RegisterMap::SCANNER_CAP_DEVICE;
        case DeviceType::CONTAINER_RACK: return RegisterMap::CONTAINER_RACK;
        case DeviceType::STIRBAR_STORAGE: return RegisterMap::STIRBAR_STORAGE;
        case DeviceType::FILTER_DEVICE: return RegisterMap::FILTER_DEVICE;
        case DeviceType::PUMP_SYSTEM: return RegisterMap::PUMP_SYSTEM;
        case DeviceType::VALVE_CONTROLLER: return RegisterMap::VALVE_CONTROLLER;
        case DeviceType::SAFETY_DOOR: return RegisterMap::SAFETY_DOOR;
        case DeviceType::EMERGENCY_STOP: return RegisterMap::EMERGENCY_STOP;
        case DeviceType::FRAME_LOCK: return RegisterMap::FRAME_LOCK;
        case DeviceType::VENTILATION_FAN: return RegisterMap::VENTILATION_FAN;
        case DeviceType::LIGHTING_SYSTEM: return RegisterMap::LIGHTING_SYSTEM;
        case DeviceType::SENSOR_ARRAY: return RegisterMap::SENSOR_ARRAY;
        default: return -1;
    }
}

bool PLCDriver::writeDeviceRegister(DeviceType device, uint16_t value) {
    int address = getDeviceRegisterAddress(device);
    if (address < 0) {
        setError("Invalid device type");
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_mapping) {
        m_mapping->tab_registers[address] = value;

        // 更新设备状态
        DeviceStatus status;
        status.type = device;
        status.isOnline = true;
        status.isRunning = (value == 1);
        status.hasFault = (value == 3);  // 3 = 故障状态
        status.rawStatus = value;

        updateDeviceStatus(device, status);
        return true;
    }
    return false;
}

uint16_t PLCDriver::readDeviceRegister(DeviceType device) {
    int address = getDeviceRegisterAddress(device);
    if (address < 0) {
        return 0;
    }

    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_mapping) {
        return m_mapping->tab_registers[address];
    }
    return 0;
}

void PLCDriver::initializeDeviceStates() {
    // 初始化所有设备状态
    std::vector<DeviceType> devices = {
        DeviceType::SAMPLE_ENTRY_CONVEYOR,
        DeviceType::SAMPLE_EXIT_CONVEYOR,
        DeviceType::SCANNER_CAP_DEVICE,
        DeviceType::CONTAINER_RACK,
        DeviceType::STIRBAR_STORAGE,
        DeviceType::FILTER_DEVICE,
        DeviceType::PUMP_SYSTEM,
        DeviceType::VALVE_CONTROLLER,
        DeviceType::SAFETY_DOOR,
        DeviceType::EMERGENCY_STOP,
        DeviceType::FRAME_LOCK,
        DeviceType::VENTILATION_FAN,
        DeviceType::LIGHTING_SYSTEM,
        DeviceType::SENSOR_ARRAY
    };

    for (auto device : devices) {
        DeviceStatus status;
        status.type = device;
        status.isOnline = false;
        status.isRunning = false;
        status.hasFault = false;
        status.rawStatus = 0;
        status.statusMessage = "Initialized";

        m_deviceStates[device] = status;
    }
}

bool PLCDriver::readHoldingRegister(int address, uint16_t& value) {
    if (!isRunning()) {
        setError("PLC server not running");
        return false;
    }

    if (address < 0 || address >= 100) {
        setError("Invalid register address: " + std::to_string(address));
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_mapping) {
        value = m_mapping->tab_registers[address];
        return true;
    }
    return false;
}

bool PLCDriver::writeHoldingRegister(int address, uint16_t value) {
    if (!isRunning()) {
        setError("PLC server not running");
        return false;
    }

    if (address < 0 || address >= 100) {
        setError("Invalid register address: " + std::to_string(address));
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_mapping) {
        m_mapping->tab_registers[address] = value;
        return true;
    }
    return false;
}

// ========== 冲洗系统控制实现 ==========

bool PLCDriver::executeTemperatureSensorFlush(int heaterChannel, int flushTimeout) {
    if (!isRunning()) {
        setError("PLC server not running");
        return false;
    }

    if (heaterChannel < 0 || heaterChannel > 10) {
        setError("Invalid heater channel: " + std::to_string(heaterChannel) + ". Must be 0-10");
        return false;
    }

    if (flushTimeout <= 0 || flushTimeout > 300) {
        setError("Invalid flush timeout: " + std::to_string(flushTimeout) + ". Must be 1-300 seconds");
        return false;
    }

    LOG(INFO) << "Starting temperature sensor flush for heater channel " << heaterChannel
              << " with timeout " << flushTimeout << " seconds";

    try {
        // 设置冲洗时间
        int timeoutAddress = RegisterMap::FLUSH_TIMEOUT_BASE + heaterChannel;
        if (!writeHoldingRegister(timeoutAddress, static_cast<uint16_t>(flushTimeout))) {
            setError("Failed to set flush timeout for channel " + std::to_string(heaterChannel));
            return false;
        }

        // 启动冲洗
        int controlAddress = RegisterMap::FLUSH_CONTROL_BASE + heaterChannel;
        if (!writeHoldingRegister(controlAddress, 1)) {
            setError("Failed to start flush for channel " + std::to_string(heaterChannel));
            return false;
        }

        LOG(INFO) << "Temperature sensor flush started successfully for channel " << heaterChannel;
        return true;

    } catch (const std::exception& e) {
        setError("Exception in executeTemperatureSensorFlush: " + std::string(e.what()));
        return false;
    }
}

bool PLCDriver::stopTemperatureSensorFlush(int heaterChannel) {
    if (!isRunning()) {
        setError("PLC server not running");
        return false;
    }

    if (heaterChannel < 0 || heaterChannel > 10) {
        setError("Invalid heater channel: " + std::to_string(heaterChannel) + ". Must be 0-10");
        return false;
    }

    LOG(INFO) << "Stopping temperature sensor flush for heater channel " << heaterChannel;

    try {
        // 停止冲洗
        int controlAddress = RegisterMap::FLUSH_CONTROL_BASE + heaterChannel;
        if (!writeHoldingRegister(controlAddress, 0)) {
            setError("Failed to stop flush for channel " + std::to_string(heaterChannel));
            return false;
        }

        LOG(INFO) << "Temperature sensor flush stopped successfully for channel " << heaterChannel;
        return true;

    } catch (const std::exception& e) {
        setError("Exception in stopTemperatureSensorFlush: " + std::string(e.what()));
        return false;
    }
}

bool PLCDriver::isTemperatureSensorFlushRunning(int heaterChannel) {
    if (!isRunning()) {
        setError("PLC server not running");
        return false;
    }

    if (heaterChannel < 0 || heaterChannel > 10) {
        setError("Invalid heater channel: " + std::to_string(heaterChannel) + ". Must be 0-10");
        return false;
    }

    try {
        // 读取冲洗状态
        int statusAddress = RegisterMap::FLUSH_STATUS_BASE + heaterChannel;
        uint16_t status;
        if (!readHoldingRegister(statusAddress, status)) {
            setError("Failed to read flush status for channel " + std::to_string(heaterChannel));
            return false;
        }

        return (status == 1);

    } catch (const std::exception& e) {
        setError("Exception in isTemperatureSensorFlushRunning: " + std::string(e.what()));
        return false;
    }
}

} // namespace PLC
} // namespace AnalysisRobot
