
#include "AnalysisRobotApp.h"
#include "DeviceManager.h"
#include "ConfigManager.h"
#include "RestInterfaceDriver.h"
#include <iostream>
#include <chrono>
#include <thread>
#include <filesystem>
#include "glog.h"

AnalysisRobotApp::AnalysisRobotApp()
    : m_running(false)
    , m_initialized(false) {
    LOG(INFO) << "Analysis Robot App created";
}

AnalysisRobotApp::~AnalysisRobotApp() {
    stop();
    LOG(INFO) << "Analysis Robot App destroyed";
}

bool AnalysisRobotApp::initialize(const std::string& configFile) {
    LOG(INFO) << "Initializing Analysis Robot App";

    try {
        // 初始化配置管理器
        if (!initializeConfig(configFile)) {
            setError("Failed to initialize config manager");
            return false;
        }

        // 初始化设备管理器
        if (!initializeDeviceManager()) {
            setError("Failed to initialize device manager");
            return false;
        }

        // 初始化REST服务器
        if (!initializeRestServer()) {
            setError("Failed to initialize REST server");
            return false;
        }

        // 注册设备控制器到REST服务器
        if (!registerDeviceControllers()) {
            setError("Failed to register device controllers");
            return false;
        }

        m_initialized = true;
        LOG(INFO) << "Analysis Robot App initialized successfully";
        return true;
        
    } catch (const std::exception& e) {
        setError("Exception during initialization: " + std::string(e.what()));
        return false;
    }
}

bool AnalysisRobotApp::start() {
    if (!m_initialized) {
        setError("App not initialized");
        return false;
    }
    
    if (m_running) {
        LOG(WARNING) << "App already running";
        return true;
    }

    LOG(INFO) << "Starting Analysis Robot App";

    try {
        // 连接所有设备
        if (!m_deviceManager->connectAllDevices()) {
            LOG(WARNING) << "Some devices failed to connect, continuing anyway";
        }

        // 启动所有设备
        if (!m_deviceManager->startAllDevices()) {
            setError("Failed to start devices");
            return false;
        }

        // 启动REST服务器
        if (!m_restServer->start()) {
            setError("Failed to start REST server: " + m_restServer->getLastError());
            return false;
        }

        // 启动监控线程
        m_running = true;
        m_monitorThread = std::make_unique<std::thread>(&AnalysisRobotApp::monitorLoop, this);

        LOG(INFO) << "Analysis Robot App started successfully";
        return true;
        
    } catch (const std::exception& e) {
        setError("Exception during startup: " + std::string(e.what()));
        return false;
    }
}

void AnalysisRobotApp::stop() {
    if (!m_running) {
        return;
    }

    LOG(INFO) << "Stopping Analysis Robot App";

    m_running = false;

    // 等待监控线程结束
    if (m_monitorThread && m_monitorThread->joinable()) {
        m_monitorThread->join();
        m_monitorThread.reset();
    }

    // 停止REST服务器
    if (m_restServer) {
        m_restServer->stop();
    }

    // 停止所有设备
    if (m_deviceManager) {
        m_deviceManager->stopAllDevices();
        m_deviceManager->disconnectAllDevices();
    }

    LOG(INFO) << "Analysis Robot App stopped";
}

bool AnalysisRobotApp::isRunning() const {
    return m_running;
}

void AnalysisRobotApp::run() {
    if (!start()) {
        LOG(ERROR) << "Failed to start app: " << getLastError();
        return;
    }

    LOG(INFO) << "Analysis Robot App is running. Press Ctrl+C to stop.";

    // 主循环 - 等待停止信号
    while (m_running) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
    }

    LOG(INFO) << "Analysis Robot App main loop ended";
}

nlohmann::json AnalysisRobotApp::getStatus() const {
    nlohmann::json status;

    status["app_name"] = "Analysis Robot";
    status["version"] = "1.0.0";
    status["running"] = m_running.load();
    status["initialized"] = m_initialized.load();
    
    // 获取当前时间
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    std::ostringstream oss;
    oss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    status["timestamp"] = oss.str();
    
    // 获取REST服务器状态
    if (m_restServer) {
        status["rest_server"]["running"] = m_restServer->isRunning();
        status["rest_server"]["info"] = m_restServer->getServerInfo();
    }
    
    // 获取设备状态
    if (m_deviceManager) {
        status["devices"] = m_deviceManager->getDevicesStatus();
    }
    
    // 获取配置信息
    if (m_configManager) {
        const auto& appConfig = m_configManager->getAppConfig();
        status["config"]["rest_server"]["host"] = appConfig.restServer.host;
        status["config"]["rest_server"]["port"] = appConfig.restServer.port;
        status["config"]["logging"]["level"] = appConfig.logging.level;
        status["config"]["monitor"]["status_update_interval"] = appConfig.monitor.statusUpdateInterval;
    }
    
    return status;
}

std::string AnalysisRobotApp::getLastError() const {
    return m_lastError;
}

bool AnalysisRobotApp::initializeConfig(const std::string& configFile) {
    LOG(INFO) << "Initializing config manager";
    LOG(INFO) << "App config file: " << configFile;

    m_configManager = std::make_unique<ConfigManager>();

    if (!m_configManager->loadConfig(configFile)) {
        setError("Failed to load app config: " + m_configManager->getLastError());
        return false;
    }

    // 加载设备配置 - 从应用配置文件路径推导设备配置文件路径
    try {
        LOG(INFO) << "Creating config path from: " << configFile;
        std::filesystem::path configPath(configFile);

        LOG(INFO) << "Getting parent path";
        std::filesystem::path parentPath = configPath.parent_path();

        LOG(INFO) << "Creating devices config path";
        std::filesystem::path devicesConfigPath = parentPath / "devices.json";

        LOG(INFO) << "Converting to string";
        std::string devicesConfigFile = devicesConfigPath.string();

        LOG(INFO) << "Devices config file: " << devicesConfigFile;

        if (!m_configManager->loadDevicesConfig(devicesConfigFile)) {
            setError("Failed to load devices config: " + m_configManager->getLastError());
            return false;
        }
    } catch (const std::exception& e) {
        setError("Exception while processing devices config path: " + std::string(e.what()));
        return false;
    }
    
    // 验证配置
    if (!m_configManager->validateConfig()) {
        setError("Config validation failed: " + m_configManager->getLastError());
        return false;
    }

    LOG(INFO) << "Config manager initialized successfully";
    return true;
}

bool AnalysisRobotApp::initializeDeviceManager() {
    LOG(INFO) << "Initializing device manager";

    m_deviceManager = std::make_unique<DeviceManager>();

    // 获取设备配置
    const DeviceConfig& devicesConfig = m_configManager->getDevicesConfig();
    LOG(INFO) << "Retrieved device config with " << devicesConfig.balances.size() << " balances, "
              << devicesConfig.stirrer_heaters.size() << " stirrer heaters";

    if (!m_deviceManager->initialize(devicesConfig)) {
        setError("Failed to initialize device manager: " + m_deviceManager->getLastError());
        return false;
    }

    LOG(INFO) << "Device manager initialized successfully";
    return true;
}

bool AnalysisRobotApp::initializeRestServer() {
    LOG(INFO) << "Initializing REST server";

    m_restServer = std::make_unique<AnalysisRobot::RestInterface::RestInterfaceDriver>();

    const AppConfig& appConfig = m_configManager->getAppConfig();

    AnalysisRobot::RestInterface::RestConfig restConfig;
    restConfig.host = appConfig.restServer.host;
    restConfig.port = appConfig.restServer.port;
    restConfig.maxConnections = appConfig.restServer.maxConnections;
    restConfig.threadPoolSize = appConfig.restServer.threadPoolSize;
    restConfig.enableLogging = appConfig.restServer.enableLogging;
    restConfig.logLevel = appConfig.restServer.logLevel;

    if (!m_restServer->initialize(restConfig)) {
        setError("Failed to initialize REST server: " + m_restServer->getLastError());
        return false;
    }

    LOG(INFO) << "REST server initialized successfully";
    return true;
}

bool AnalysisRobotApp::registerDeviceControllers() {
    LOG(INFO) << "Registering device controllers";

    auto deviceControllers = m_deviceManager->getAllDeviceControllers();
    LOG(INFO) << "Retrieved " << deviceControllers.size() << " device controllers from device manager";

    if (deviceControllers.empty()) {
        LOG(WARNING) << "No device controllers found to register";
        return true;  // 不算错误，可能是配置中没有启用设备
    }

    for (const auto& pair : deviceControllers) {
        LOG(INFO) << "Registering device controller: " << pair.first;
        m_restServer->registerDeviceController(pair.first, pair.second);
        LOG(INFO) << "Successfully registered device controller: " << pair.first;
    }

    LOG(INFO) << "All " << deviceControllers.size() << " device controllers registered successfully";
    return true;
}

void AnalysisRobotApp::monitorLoop() {
    LOG(INFO) << "Monitor thread started";

    const AppConfig& config = m_configManager->getAppConfig();
    const int statusUpdateInterval = config.monitor.statusUpdateInterval;
    const int healthCheckInterval = config.monitor.healthCheckInterval;

    auto lastStatusUpdate = std::chrono::steady_clock::now();
    auto lastHealthCheck = std::chrono::steady_clock::now();
    auto lastReconnectAttempt = std::chrono::steady_clock::now() - std::chrono::minutes(1); // 初始化为1分钟前

    while (m_running) {
        auto now = std::chrono::steady_clock::now();

        // 状态更新
        if (std::chrono::duration_cast<std::chrono::seconds>(now - lastStatusUpdate).count() >= statusUpdateInterval) {
            try {
                // 这里可以添加定期状态更新逻辑
                // 例如：更新设备状态缓存、检查连接状态等
                lastStatusUpdate = now;
            } catch (const std::exception& e) {
                LOG(ERROR) << "Exception in status update: " << e.what();
            }
        }

        // 健康检查
        if (std::chrono::duration_cast<std::chrono::seconds>(now - lastHealthCheck).count() >= healthCheckInterval) {
            try {
                // 检查设备连接状态
                if (m_deviceManager && !m_deviceManager->areAllDevicesConnected()) {
                    LOG(WARNING) << "Some devices are not connected";

                    // 如果启用自动重连，并且距离上次重连尝试超过30秒
                    if (config.monitor.enableAutoReconnect) {
                        auto timeSinceLastReconnect = std::chrono::duration_cast<std::chrono::seconds>(now - lastReconnectAttempt).count();
                        if (timeSinceLastReconnect >= 30) {
                            LOG(INFO) << "Attempting to reconnect devices (last attempt was " << timeSinceLastReconnect << " seconds ago)";
                            m_deviceManager->connectAllDevices();
                            lastReconnectAttempt = now;
                        } else {
                            LOG(INFO) << "Skipping reconnect attempt, too soon since last attempt (" << timeSinceLastReconnect << " seconds)";
                        }
                    }
                }

                // 检查REST服务器状态
                if (m_restServer && !m_restServer->isRunning()) {
                    LOG(ERROR) << "REST server is not running";
                }

                lastHealthCheck = now;
            } catch (const std::exception& e) {
                LOG(ERROR) << "Exception in health check: " << e.what();
            }
        }

        // 短暂休眠
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    LOG(INFO) << "Monitor thread ended";
}

void AnalysisRobotApp::setError(const std::string& error) {
    m_lastError = error;
    LOG(ERROR) << error;
}
