#ifndef ANALYSIS_ROBOT_APP_H
#define ANALYSIS_ROBOT_APP_H

#include <memory>
#include <string>
#include <atomic>
#include <thread>
#include <nlohmann/json.hpp>


// 前向声明
namespace AnalysisRobot {
namespace RestInterface {
    class RestInterfaceDriver;
}
}

class DeviceManager;
class ConfigManager;

/**
 * @brief Analysis Robot 主应用程序类
 * 
 * 负责整个系统的初始化、启动、停止和管理
 * 集成REST接口服务器和所有硬件设备驱动
 */
class AnalysisRobotApp {
public:
    /**
     * @brief 构造函数
     */
    AnalysisRobotApp();
    
    /**
     * @brief 析构函数
     */
    ~AnalysisRobotApp();
    
    /**
     * @brief 初始化应用程序
     * @param configFile 配置文件路径
     * @return 是否成功
     */
    bool initialize(const std::string& configFile = "config/app_config.json");
    
    /**
     * @brief 启动应用程序
     * @return 是否成功
     */
    bool start();
    
    /**
     * @brief 停止应用程序
     */
    void stop();
    
    /**
     * @brief 检查应用程序是否运行中
     * @return 是否运行中
     */
    bool isRunning() const;
    
    /**
     * @brief 运行应用程序主循环
     * 阻塞调用，直到应用程序停止
     */
    void run();
    
    /**
     * @brief 获取应用程序状态信息
     * @return 状态信息JSON
     */
    nlohmann::json getStatus() const;
    
    /**
     * @brief 获取最后的错误信息
     * @return 错误信息
     */
    std::string getLastError() const;

private:
    std::atomic<bool> m_running;                                    // 运行状态
    std::atomic<bool> m_initialized;                               // 初始化状态
    
    std::unique_ptr<ConfigManager> m_configManager;                // 配置管理器
    std::unique_ptr<DeviceManager> m_deviceManager;                // 设备管理器
    std::unique_ptr<AnalysisRobot::RestInterface::RestInterfaceDriver> m_restServer; // REST服务器
    
    std::unique_ptr<std::thread> m_monitorThread;                  // 监控线程
    
    std::string m_lastError;                                       // 最后错误信息
    
    /**
     * @brief 初始化配置管理器
     * @param configFile 配置文件路径
     * @return 是否成功
     */
    bool initializeConfig(const std::string& configFile);
    
    /**
     * @brief 初始化设备管理器
     * @return 是否成功
     */
    bool initializeDeviceManager();
    
    /**
     * @brief 初始化REST服务器
     * @return 是否成功
     */
    bool initializeRestServer();
    
    /**
     * @brief 注册所有设备控制器到REST服务器
     * @return 是否成功
     */
    bool registerDeviceControllers();
    
    /**
     * @brief 系统监控线程函数
     */
    void monitorLoop();
    
    /**
     * @brief 设置错误信息
     * @param error 错误信息
     */
    void setError(const std::string& error);
};

#endif // ANALYSIS_ROBOT_APP_H
